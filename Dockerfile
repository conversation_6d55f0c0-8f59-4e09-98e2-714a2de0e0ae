# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use an official Node.js image as a base
FROM node:20-alpine AS builder

# Set the working directory in the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package.json tsconfig.json ./

# Install project dependencies
RUN --mount=type=cache,target=/root/.npm npm install

# Copy the rest of the application's source code
COPY src/ ./src/

# Build the TypeScript project
RUN npm run build

# Use a lightweight Node.js image for the production build
FROM node:20-alpine

# Set the working directory in the container
WORKDIR /app

# Copy the built files and node_modules from the builder stage
COPY --from=builder /app/build ./build
COPY --from=builder /app/node_modules ./node_modules

# Expose the port on which the server will run (assume 3000, replace if necessary)
EXPOSE 3000

# Set the environment variables for the PocketBase connection
ENV POCKETBASE_URL=http://127.0.0.1:8090

# Start the server
CMD ["node", "build/index.js"]

{"name": "pocketbase-server", "version": "0.1.0", "type": "module", "scripts": {"build": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "start": "node build/index.js", "dev": "tsc -w", "test": "node test/autodate-fields.test.js", "test:autodate": "node test/autodate-fields.test.js"}, "dependencies": {"@modelcontextprotocol/sdk": "^0.6.1", "pocketbase": "^0.25.2"}, "devDependencies": {"@types/node": "^20.17.24", "typescript": "^5.3.3"}}
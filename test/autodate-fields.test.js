#!/usr/bin/env node

/**
 * Simple test script to verify autodate field creation and validation
 * This is a basic test to ensure our autodate field logic works correctly
 */

// Mock PocketBase field validation functions
class AutodateFieldValidator {
  /**
   * Helper method to generate unique autodate field IDs following PocketBase pattern
   */
  generateAutodateFieldId(suffix) {
    return `autodate${Math.floor(Math.random() * 10000000000)}${suffix}`;
  }

  /**
   * Helper method to create a properly configured autodate field
   */
  createAutodateField(name, customId) {
    const isCreatedField = name === 'created';
    return {
      hidden: false,
      id: customId || this.generateAutodateFieldId(`_${name}`),
      name: name,
      onCreate: true,
      onUpdate: isCreatedField ? false : true,
      presentable: false,
      system: false,
      type: "autodate"
    };
  }

  /**
   * Helper method to validate and fix autodate field configuration
   */
  validateAutodateField(field) {
    if (field.type !== 'autodate') {
      return field;
    }

    const validatedField = {
      ...field,
      hidden: field.hidden !== undefined ? field.hidden : false,
      presentable: field.presentable !== undefined ? field.presentable : false,
      system: field.system !== undefined ? field.system : false,
      type: "autodate"
    };

    // Ensure proper onCreate/onUpdate flags for standard autodate fields
    if (field.name === 'created') {
      validatedField.onCreate = true;
      validatedField.onUpdate = false;
    } else if (field.name === 'updated') {
      validatedField.onCreate = true;
      validatedField.onUpdate = true;
    } else {
      // For custom autodate fields, preserve user settings or set defaults
      validatedField.onCreate = field.onCreate !== undefined ? field.onCreate : true;
      validatedField.onUpdate = field.onUpdate !== undefined ? field.onUpdate : false;
    }

    // Generate ID if missing
    if (!validatedField.id) {
      validatedField.id = this.generateAutodateFieldId(`_${field.name || 'autodate'}`);
    }

    return validatedField;
  }
}

// Test functions
function testAutodateFieldCreation() {
  console.log('🧪 Testing autodate field creation...');
  
  const validator = new AutodateFieldValidator();
  
  // Test created field
  const createdField = validator.createAutodateField('created');
  console.log('✅ Created field:', JSON.stringify(createdField, null, 2));
  
  // Verify created field properties
  if (createdField.name !== 'created' || 
      createdField.type !== 'autodate' ||
      createdField.onCreate !== true ||
      createdField.onUpdate !== false) {
    throw new Error('❌ Created field has incorrect properties');
  }
  
  // Test updated field
  const updatedField = validator.createAutodateField('updated');
  console.log('✅ Updated field:', JSON.stringify(updatedField, null, 2));
  
  // Verify updated field properties
  if (updatedField.name !== 'updated' || 
      updatedField.type !== 'autodate' ||
      updatedField.onCreate !== true ||
      updatedField.onUpdate !== true) {
    throw new Error('❌ Updated field has incorrect properties');
  }
  
  console.log('✅ Autodate field creation tests passed!');
}

function testAutodateFieldValidation() {
  console.log('🧪 Testing autodate field validation...');
  
  const validator = new AutodateFieldValidator();
  
  // Test validation of incomplete created field
  const incompleteCreatedField = {
    name: 'created',
    type: 'autodate'
    // Missing other properties
  };
  
  const validatedCreated = validator.validateAutodateField(incompleteCreatedField);
  console.log('✅ Validated created field:', JSON.stringify(validatedCreated, null, 2));
  
  // Verify validation fixed the field
  if (validatedCreated.onCreate !== true || 
      validatedCreated.onUpdate !== false ||
      !validatedCreated.id ||
      validatedCreated.hidden !== false) {
    throw new Error('❌ Created field validation failed');
  }
  
  // Test validation of incomplete updated field
  const incompleteUpdatedField = {
    name: 'updated',
    type: 'autodate',
    onCreate: false, // Wrong value
    onUpdate: false  // Wrong value
  };
  
  const validatedUpdated = validator.validateAutodateField(incompleteUpdatedField);
  console.log('✅ Validated updated field:', JSON.stringify(validatedUpdated, null, 2));
  
  // Verify validation corrected the field
  if (validatedUpdated.onCreate !== true || 
      validatedUpdated.onUpdate !== true ||
      !validatedUpdated.id) {
    throw new Error('❌ Updated field validation failed');
  }
  
  // Test validation of non-autodate field (should pass through unchanged)
  const textField = {
    name: 'title',
    type: 'text',
    required: true
  };
  
  const validatedText = validator.validateAutodateField(textField);
  if (JSON.stringify(validatedText) !== JSON.stringify(textField)) {
    throw new Error('❌ Non-autodate field was modified during validation');
  }
  
  console.log('✅ Autodate field validation tests passed!');
}

function testCollectionFieldProcessing() {
  console.log('🧪 Testing collection field processing...');
  
  const validator = new AutodateFieldValidator();
  
  // Simulate user fields without autodate fields
  const userFields = [
    { name: 'title', type: 'text', required: true },
    { name: 'content', type: 'text' }
  ];
  
  // Process fields like in createCollection
  const validatedUserFields = userFields.map(field => validator.validateAutodateField(field));
  
  const hasCreatedField = validatedUserFields.some(field => 
    field.name === 'created' && field.type === 'autodate'
  );
  const hasUpdatedField = validatedUserFields.some(field => 
    field.name === 'updated' && field.type === 'autodate'
  );
  
  const defaultFields = [];
  if (!hasCreatedField) {
    defaultFields.push(validator.createAutodateField('created'));
  }
  if (!hasUpdatedField) {
    defaultFields.push(validator.createAutodateField('updated'));
  }
  
  const finalFields = [...validatedUserFields, ...defaultFields];
  
  console.log('✅ Final fields:', JSON.stringify(finalFields, null, 2));
  
  // Verify we have all expected fields
  if (finalFields.length !== 4) {
    throw new Error('❌ Expected 4 fields (2 user + 2 autodate)');
  }
  
  const hasCreated = finalFields.some(f => f.name === 'created' && f.type === 'autodate');
  const hasUpdated = finalFields.some(f => f.name === 'updated' && f.type === 'autodate');
  
  if (!hasCreated || !hasUpdated) {
    throw new Error('❌ Missing autodate fields in final result');
  }
  
  console.log('✅ Collection field processing tests passed!');
}

// Run all tests
function runTests() {
  console.log('🚀 Starting autodate field tests...\n');
  
  try {
    testAutodateFieldCreation();
    console.log('');
    
    testAutodateFieldValidation();
    console.log('');
    
    testCollectionFieldProcessing();
    console.log('');
    
    console.log('🎉 All tests passed! Autodate field implementation is working correctly.');
  } catch (error) {
    console.error('💥 Test failed:', error.message);
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests();
}

export { AutodateFieldValidator, runTests };
